@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

@layer base {
    :root {
        --background: hsl(0 0% 100%);
        --foreground: hsl(240 10% 3.9%);
        --card: hsl(0 0% 100%);
        --card-foreground: hsl(240 10% 3.9%);
        --popover: hsl(0 0% 100%);
        --popover-foreground: hsl(240 10% 3.9%);
        --primary: hsl(240 5.9% 10%);
        --primary-foreground: hsl(0 0% 98%);
        --secondary: hsl(240 4.8% 95.9%);
        --secondary-foreground: hsl(240 5.9% 10%);
        --muted: hsl(240 4.8% 95.9%);
        --muted-foreground: hsl(240 3.8% 46.1%);
        --accent: hsl(240 4.8% 95.9%);
        --accent-foreground: hsl(240 5.9% 20%);
        --destructive: hsl(0 84.2% 60.2%);
        --destructive-foreground: hsl(0 0% 98%);
        --border: hsl(240 5.9% 90%);
        --input: hsl(240 5.9% 90%);
        --ring: hsl(240 10% 3.9%);
        --chart-1: hsl(12 76% 61%);
        --chart-2: hsl(173 58% 39%);
        --chart-3: hsl(197 37% 24%);
        --chart-4: hsl(43 74% 66%);
        --chart-5: hsl(27 87% 67%);
        --radius: 0.5rem;
    }

    .dark {
        --background: hsl(240, 10%, 12%);
        --foreground: hsl(0 0% 98%);
        --card: hsl(240 10% 3.9%);
        --card-foreground: hsl(0 0% 98%);
        --popover: hsl(240 10% 3.9%);
        --popover-foreground: hsl(0 0% 98%);
        --primary: hsl(245, 70%, 60%);
        --primary-foreground: hsl(272 71.7% 70%);
        --secondary: hsl(240 3.7% 5%);
        --secondary-foreground: hsl(0 0% 98%);
        --muted: hsl(240 3.7% 15.9%);
        --muted-foreground: hsl(240 5% 64.9%);
        --accent: hsl(240 3.7% 25%);
        --accent-foreground: hsl(0 0% 98%);
        --destructive: hsl(0 62.8% 50.6%);
        --destructive-foreground: hsl(0 0% 98%);
        --border: hsl(240 3.7% 15.9%);
        --input: hsl(240 3.7% 15.9%);
        --ring: hsl(240 4.9% 83.9%);
        --chart-1: hsl(220 70% 50%);
        --chart-2: hsl(160 60% 45%);
        --chart-3: hsl(30 80% 55%);
        --chart-4: hsl(280 65% 60%);
        --chart-5: hsl(340 75% 55%);
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

@keyframes gradient {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Page sliding animations */
@keyframes slideOutLeft {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(-100%);
        opacity: 0;
    }
}

@keyframes slideOutRight {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes slideInLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.animate-slide-out-left {
    animation: slideOutLeft 0.3s ease-in-out forwards;
    position: absolute;
    width: 100%;
}

.animate-slide-out-right {
    animation: slideOutRight 0.3s ease-in-out forwards;
    position: absolute;
    width: 100%;
}

.animate-slide-in-left {
    animation: slideInLeft 0.3s ease-in-out forwards;
    position: absolute;
    width: 100%;
}

.animate-slide-in-right {
    animation: slideInRight 0.3s ease-in-out forwards;
    position: absolute;
    width: 100%;
}

