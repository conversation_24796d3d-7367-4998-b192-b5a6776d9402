/** @type {import('next').NextConfig} */
const nextConfig = {
    rewrites: async () => {
        return [
            {
                source: '/api/:path*',
                destination:
                  process.env.NODE_ENV === 'development'
                    ? 'http://127.0.0.1:5000/api/:path*'
                    : '/api/',
            },
            {
                source: '/static/:path*',
                destination:
                  process.env.NODE_ENV === 'development'
                    ? 'http://127.0.0.1:5000/static/:path*'
                    : '/static/',
            },
        ]
    },
}

export default nextConfig;
