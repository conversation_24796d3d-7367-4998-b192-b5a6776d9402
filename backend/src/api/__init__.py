from appwrite.exception import AppwriteException
from flask import Blueprint, g, jsonify, abort

from .util import *


def obfuscate_email(email: str) -> str:
    """
    Obfuscates an email by partially masking both the local part and the domain.
    For example, "<EMAIL>" becomes "joh*****@gmail.com".
    """
    local, domain = email.split("@")

    # Obfuscate local part: keep first and last character, mask the middle
    if len(local) > 3:
        local = local[0] + "*" * (len(local) - 2) + local[-1]
    else:
        # If only one or two chars, just mask whatever remains after the first
        local = local[0] + "*" * (len(local) - 1)

    return f"{local}@{domain}"


admin_client = get_client().set_key(os.environ['API_KEY'])
admin_account = Account(admin_client)

api = Blueprint('api', __name__, url_prefix='/api')
needs_login = Blueprint('needs_login', __name__)
api.register_blueprint(needs_login)

def is_logged_in():
    try:
        g.account = get_user_account().get()
    except AttributeError:
        g.account = None
    return g.account is not None


@needs_login.before_request
def check_login():
    if not is_logged_in():
        abort(401, "You must be logged in to access this resource")


@api.errorhandler(AppwriteException)
def handle_unauthorized(e):
    if "User (role: guests) missing scope (account)" in str(e):
        return {"error": str(e), "needs_login": True}, 401
    if "with the requested ID could not be found." in str(e):
        return {"error": str(e), "not_found": True}, 404
    raise e


def logged_in():
    def wrapper(func, *args, **kwargs):
        if not is_logged_in():
            return {"error": "You must be logged in to access this resource"}, 401
        return func(*args, **kwargs)
    return wrapper


from .accounts import acc
from .documents import documents
from .scoring import audio

api.register_blueprint(acc)
needs_login.register_blueprint(documents)
needs_login.register_blueprint(audio)
